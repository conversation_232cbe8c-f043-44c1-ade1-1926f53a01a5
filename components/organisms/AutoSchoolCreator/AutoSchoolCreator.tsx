'use client';

import { useSession } from 'next-auth/react';
import { useEffect, useState } from 'react';
import { autoCreateSchoolForCurrentUser } from '@/actions/school.action';
import { EUserRole } from '@/config/enums/user';

/**
 * AutoSchoolCreator component that automatically creates a school for new INDEPENDENT_TEACHER users
 * who don't have a school yet. This runs after successful sign-in.
 */
export default function AutoSchoolCreator() {
  const { data: session, status, update } = useSession();
  const [isCreatingSchool, setIsCreatingSchool] = useState(false);
  const [hasAttemptedCreation, setHasAttemptedCreation] = useState(false);

  useEffect(() => {
    // Only run if session is loaded and user is authenticated
    if (status !== 'authenticated' || !session?.user) {
      return;
    }

    // Only run for INDEPENDENT_TEACHER users without a school
    if (
      session.user.role !== EUserRole.INDEPENDENT_TEACHER ||
      session.user.schoolId ||
      hasAttemptedCreation ||
      isCreatingSchool
    ) {
      return;
    }

    // Auto-create school for new users
    const createSchool = async () => {
      setIsCreatingSchool(true);
      setHasAttemptedCreation(true);

      try {
        console.log('🏫 Auto-creating school for new user:', session.user.id);
        
        const result = await autoCreateSchoolForCurrentUser();

        if (result.status === 'success' && result.data) {
          console.log('✅ School auto-created successfully:', result.data.id);
          
          // Update the session with the new school information
          await update({
            user: {
              schoolId: result.data.id,
              school: {
                id: result.data.id,
                name: result.data.name,
                address: result.data.address,
                phoneNumber: result.data.phoneNumber,
                registeredNumber: result.data.registeredNumber,
                email: result.data.email,
                brand: result.data.brand,
              }
            }
          });

          console.log('✅ Session updated with auto-created school');
        } else {
          console.error('❌ Failed to auto-create school:', result.message);
        }
      } catch (error) {
        console.error('❌ Error during auto school creation:', error);
      } finally {
        setIsCreatingSchool(false);
      }
    };

    createSchool();
  }, [session, status, hasAttemptedCreation, isCreatingSchool, update]);

  // This component doesn't render anything visible
  return null;
}

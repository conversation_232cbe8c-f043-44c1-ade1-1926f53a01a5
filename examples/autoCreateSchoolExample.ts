// Example usage of autoCreateSchoolForUser function
// This file demonstrates how to use the auto-school creation functionality

import { autoCreateSchoolForUser, autoCreateSchoolForUserWithSession } from '@/actions/school.action';
import { useSession } from 'next-auth/react';

// Example 1: Server-side usage (without session update)
export async function serverSideExample() {
  const userData = {
    id: 'user-uuid-123',
    name: '<PERSON>',
    email: '<EMAIL>'
  };

  const result = await autoCreateSchoolForUser(userData);
  
  if (result.status === 'success') {
    console.log('School created successfully:', result.data);
    // School name will be "<PERSON>'s School"
    // Address will be "Not specified"
    // Email will be "<EMAIL>"
  } else {
    console.error('Failed to create school:', result.message);
  }
}

// Example 2: Client-side usage with session update
export function ClientSideExample() {
  const { data: session, update } = useSession();

  const handleCreateSchool = async () => {
    if (!session?.user) {
      console.error('No user session found');
      return;
    }

    const userData = {
      id: session.user.id,
      name: session.user.name,
      email: session.user.email
    };

    // Option A: Use the wrapper function
    const result = await autoCreateSchoolForUserWithSession(userData, update);

    // Option B: Use the main function with update parameter
    // const result = await autoCreateSchoolForUser(userData, update);

    if (result.status === 'success') {
      console.log('School created and session updated:', result.data);
      // Session will now include the new school information
      // User will be redirected to dashboard with school access
    } else {
      console.error('Failed to create school:', result.message);
    }
  };

  return (
    <button onClick={handleCreateSchool}>
      Create My School
    </button>
  );
}

// Example 3: Usage during registration flow
export async function registrationFlowExample(newUserData: {
  id: string;
  name: string;
  email: string;
}) {
  try {
    // Step 1: User registration is complete
    console.log('User registered successfully:', newUserData.id);

    // Step 2: Auto-create school for the new user
    const schoolResult = await autoCreateSchoolForUser({
      id: newUserData.id,
      name: newUserData.name,
      email: newUserData.email
    });

    if (schoolResult.status === 'success') {
      console.log('✅ School auto-created for new user:', schoolResult.data);
      
      // Step 3: User can now be redirected to dashboard
      // The backend has already associated the user with the school
      // Session will be updated on next login/refresh
      
      return {
        success: true,
        user: newUserData,
        school: schoolResult.data
      };
    } else {
      console.error('❌ Failed to auto-create school:', schoolResult.message);
      
      // Handle error - could rollback user creation or flag for manual setup
      return {
        success: false,
        error: schoolResult.message,
        user: newUserData
      };
    }
  } catch (error) {
    console.error('Registration flow error:', error);
    return {
      success: false,
      error: 'Unexpected error during registration',
      user: newUserData
    };
  }
}

// Example 4: Error handling scenarios
export async function errorHandlingExample() {
  // Test with missing required fields
  try {
    const invalidData = {
      id: '', // Invalid: empty ID
      name: 'Test User',
      email: 'invalid-email' // Invalid: not a valid email
    };

    const result = await autoCreateSchoolForUser(invalidData);
    
    if (result.status === 'error') {
      console.log('Expected validation error:', result.message);
      // Output: "Invalid user data: Invalid user ID format, Invalid email format"
    }
  } catch (error) {
    console.error('Unexpected error:', error);
  }

  // Test with missing name (should use fallback)
  try {
    const dataWithoutName = {
      id: 'valid-uuid-123',
      name: null, // No name provided
      email: '<EMAIL>'
    };

    const result = await autoCreateSchoolForUser(dataWithoutName);
    
    if (result.status === 'success') {
      console.log('School created with fallback name:', result.data.name);
      // Output: School name will be "User's School"
    }
  } catch (error) {
    console.error('Unexpected error:', error);
  }
}
